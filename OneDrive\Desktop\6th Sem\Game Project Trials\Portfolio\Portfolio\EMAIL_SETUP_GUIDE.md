# Email Setup Guide for Contact Form

## How to Receive Messages from Your Website

Your contact form is now set up to send emails directly to **akashkan<PERSON><PERSON>@gmail.com** using EmailJS. Here's how to complete the setup:

## Option 1: EmailJS Setup (Recommended - FREE)

### Step 1: Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up with your email (<EMAIL>)
3. Verify your email address

### Step 2: Create Email Service
1. In EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose "Gmail" 
4. Connect your Gmail account (<EMAIL>)
5. Note down the **Service ID** (something like "service_xxxxxxx")

### Step 3: Create Email Template
1. Go to "Email Templates"
2. Click "Create New Template"
3. Use this template:

```
Subject: New Contact Form Message from {{from_name}}

From: {{from_name}} ({{from_email}})
Subject: {{subject}}
Project Type: {{project_type}}
Budget: {{budget}}

Message:
{{message}}

---
This message was sent from your portfolio website contact form.
```

4. Note down the **Template ID** (something like "template_xxxxxxx")

### Step 4: Get Public Key
1. Go to "Account" → "General"
2. Copy your **Public Key** (something like "xxxxxxxxxxxxxxx")

### Step 5: Update Your Website
1. Open `contact.js` file
2. Replace the placeholder values:

```javascript
const EMAILJS_CONFIG = {
    serviceID: 'your_actual_service_id',
    templateID: 'your_actual_template_id', 
    publicKey: 'your_actual_public_key'
};
```

### Step 6: Test Your Form
1. Open your website
2. Fill out the contact form
3. Submit it
4. Check your email (<EMAIL>) for the message

## Option 2: Alternative Solutions

### Formspree (Also Free)
1. Go to [https://formspree.io/](https://formspree.io/)
2. Create account and get form endpoint
3. Update form action in HTML

### Netlify Forms (If hosting on Netlify)
1. Add `netlify` attribute to form
2. Automatic form handling

## Current Contact Information Updated

✅ Email: <EMAIL>
✅ Phone: +91 **********
✅ Location: Bengaluru, India

## What Happens When Someone Contacts You

1. **Visitor fills form** → Form validates input
2. **Form submits** → EmailJS sends <NAME_EMAIL>
3. **You receive email** → Contains all form details
4. **You respond** → Reply directly to visitor's email

## Benefits of This Setup

- ✅ **Free** (EmailJS free tier: 200 emails/month)
- ✅ **No backend required** (works with static hosting)
- ✅ **Secure** (no email credentials in your code)
- ✅ **Professional** (emails come from your domain)
- ✅ **Reliable** (EmailJS handles delivery)

## Troubleshooting

**Form shows success but no email received:**
- Check spam folder
- Verify EmailJS configuration
- Check EmailJS dashboard for delivery status

**Form shows error:**
- Check browser console for errors
- Verify internet connection
- Check EmailJS service status

## Need Help?

If you need help setting this up, you can:
1. Follow EmailJS documentation: https://www.emailjs.com/docs/
2. Contact EmailJS support
3. Use alternative contact methods (phone/direct email)

---

**Note:** Until you complete the EmailJS setup, the form will show success messages but won't actually send emails. Visitors can still contact you <NAME_EMAIL> or +91 **********.
