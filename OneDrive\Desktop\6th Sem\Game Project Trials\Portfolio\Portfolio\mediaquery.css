/* ===== RESPONSIVE DESIGN - MEDIA QUERIES ===== */

/* Large Desktop (1200px and up) */
@media screen and (min-width: 1200px) {
    .herobox {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    nav {
        padding: 0 5%;
    }
}

/* Desktop (992px to 1199px) */
@media screen and (max-width: 1199px) {
    .herobox {
        padding: 0 3%;
    }
    
    .passion_1, .passion_2 {
        font-size: 3.5rem;
    }
}

/* Tablet (768px to 991px) */
@media screen and (max-width: 991px) {
    nav ul {
        display: none;
    }
    
    .toggle-icon {
        display: block;
    }
    
    .herobox {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }
    
    .herobox_image {
        order: -1;
        margin-bottom: 2rem;
    }
    
    .passion_1, .passion_2 {
        font-size: 2.5rem;
        margin: 1rem 0;
    }
    
    .designer_btn, .photographer_btn {
        width: 100%;
        margin: 1rem 0;
    }
}

/* Mobile Large (576px to 767px) */
@media screen and (max-width: 767px) {
    nav {
        padding: 1rem;
    }
    
    .nav_logo img {
        height: 40px;
    }
    
    .contact_btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }
    
    .herobox {
        padding: 1rem;
    }
    
    .intro a {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
    }
    
    .passion_1, .passion_2 {
        font-size: 2rem;
    }
    
    .herobox p {
        font-size: 1rem;
        margin: 1rem 0;
    }
    
    /* Card containers responsive */
    .card-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .carousel-wrapper {
        overflow-x: auto;
    }
    
    .slides-holder {
        min-width: 300px;
    }
    
    .slide {
        min-width: 250px;
    }
}

/* Mobile Small (up to 575px) */
@media screen and (max-width: 575px) {
    .herobox {
        padding: 0.5rem;
    }
    
    .passion_1, .passion_2 {
        font-size: 1.5rem;
        line-height: 1.2;
    }
    
    .intro a {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
    }
    
    .designer_btn, .photographer_btn {
        font-size: 0.9rem;
        padding: 0.8rem 1.5rem;
    }
    
    /* Navigation adjustments */
    .toggle-menu {
        width: 100%;
        right: 0;
    }
    
    .toggle-menu ul {
        padding: 2rem 1rem;
    }
    
    .toggle-menu li {
        margin: 1rem 0;
    }
    
    /* Section adjustments */
    .section-1, .section-2, .section-7 {
        padding: 2rem 1rem;
    }
    
    .section-1 h1, .section-2 h1 {
        font-size: 2rem;
    }
    
    .section-1 p {
        font-size: 0.9rem;
        line-height: 1.5;
    }
}

/* Extra Small Mobile (up to 375px) */
@media screen and (max-width: 375px) {
    .passion_1, .passion_2 {
        font-size: 1.2rem;
    }
    
    .intro a {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }
    
    .herobox_image img {
        max-width: 200px;
    }
    
    .designer_btn, .photographer_btn {
        font-size: 0.8rem;
        padding: 0.6rem 1rem;
    }
}

/* Landscape orientation adjustments */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .herobox {
        flex-direction: row;
        align-items: center;
    }
    
    .herobox_image {
        order: 0;
        margin: 0 2rem;
    }
    
    .passion_1, .passion_2 {
        font-size: 1.5rem;
    }
}

/* Print styles */
@media print {
    .toggle-icon, .toggle-menu, .button-loader, .loader_wrapper, .cursor {
        display: none !important;
    }
    
    nav {
        position: static;
    }
    
    .herobox {
        page-break-inside: avoid;
    }
}
